import ArrowLeftIcon from '@/assets/icons/arrow-left.svg'
import { Text } from "@/components/ui/Text/Text"
import { useRouter } from "expo-router"
import { TouchableOpacity, View } from "react-native"

export const HeaderBarDetail = () => {
    const router = useRouter()

    return <View className="flex flex-row items-center justify-between gap-2 px-4 py-3 ">
        <TouchableOpacity
            onPressIn={() => {
                if (router.canGoBack()) {
                    router.back()
                } else {
                    router.replace('/')
                }
            }}
        >
            <ArrowLeftIcon width={16} height={16} />
        </TouchableOpacity>

        <View className="items-center">
            <Text size="body3" variant="primary">
                Khoa nội tổng quát
            </Text>

            <Text size="body9" variant="subdued">
                (総合内科)
            </Text>
        </View>

        <View className="h-[16px] w-[16px]"></View>
    </View>
}
