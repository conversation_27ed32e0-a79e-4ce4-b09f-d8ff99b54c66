import { Text } from '@/components/ui/Text/Text'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'

export const HeaderDetail = () => {
  const { t } = useTranslation()

  return (
    <View className="flex-row items-center justify-center">
      <View className="flex-1 items-center border-b border-primary p-2 pb-3">
        <Text size="body8" variant="primary">
          Bệnh nhân
        </Text>

        <Text size="body8" variant="primary">
          患者
        </Text>
      </View>

      <View className="flex-1 items-center border-b border-[#DDDDEE] p-2 pb-3">
        <Text size="body8" variant="subdued">
          <PERSON><PERSON><PERSON> s<PERSON>
        </Text>

        <Text size="body8" variant="subdued">
          医師
        </Text>
      </View>
    </View>
  )
}
