import { Text } from "@/components/ui/Text/Text"
import { useTranslation } from "react-i18next"
import { TouchableOpacity, View } from "react-native"

import VolumeIcon from '@/assets/icons/volume-high.svg'

import EditIcon from '@/assets/icons/edit-gray.svg'
import TrashIcon from '@/assets/icons/trash-gray.svg'
import { LocaleEnum } from "@/enums/locale.enum"
import { useAppLanguage } from "@/hooks/common/useAppLanguage"
import { LocalizeField } from "@/types/global.type"
import { useMemo } from "react"
import { useParseTemplate } from "../../hooks/useParseTemplate"
import { useMedicalFacultiesStore } from "../../stores/MedicalFacultiesStores"
import { QuickEditor } from "./QuickEdit"

export const BoxSentences = () => {
    const { t } = useTranslation()

    const { getSentence, getKeywords } = useMedicalFacultiesStore()

    const { primaryLanguage, secondaryLanguage } = useAppLanguage()

    const keyWordMap = useMemo(() => {
        return Object.values(getKeywords()).reduce((acc, keyword) => {
            acc[keyword.id] = keyword.name as unknown as LocalizeField<string>
            return acc
        }, {} as Record<string, LocalizeField<string>>)
    }, [getKeywords])

    return <View className="mt-4 gap-3 w-full">

        {/* Tiếng việt */}
        <View className="flex-row gap-2 justify-between items-center">
            <Text size='body6'>
                {t('MES-46')}
            </Text>

            <TouchableOpacity>
                <VolumeIcon className="size-5" />
            </TouchableOpacity>
        </View>
        <View className="p-4 gap-4 bg-[#F9F9FC] rounded-lg">
            <TemplateRenderer locale={primaryLanguage as LocaleEnum} keyMap={keyWordMap} template={getSentence()[primaryLanguage]} />
            <View className="flex-row justify-between gap-2 items-center">

                <QuickEditor />
                <View className="flex-row gap-3 items-center">
                    <TouchableOpacity>
                        <EditIcon className="size-5" />
                    </TouchableOpacity>
                    <TouchableOpacity>
                        <TrashIcon className="size-5" />
                    </TouchableOpacity>
                </View>
            </View>
        </View>

        {/* Tiếng nhật */}

        <View className="flex-row gap-2 justify-between items-center">
            <Text size='body6'>
                {t('MES-47')}
            </Text>

            <TouchableOpacity>
                <VolumeIcon className="size-5" />
            </TouchableOpacity>
        </View>
        <View className="p-4 gap-4 bg-[#F9F9FC] rounded-lg">
            <TemplateRenderer locale={secondaryLanguage as LocaleEnum} keyMap={keyWordMap} template={getSentence()[secondaryLanguage]} />

        </View>
    </View>
}



const TemplateRenderer: React.FC<{ template: string, keyMap: Record<string, LocalizeField<string>>, locale: LocaleEnum }> = ({ template, keyMap, locale }) => {
    const { parseTemplate } = useParseTemplate()

    const parsedParts = useMemo(() => parseTemplate(template), [template, parseTemplate]);


    return (

        <Text size='body7'>
            {parsedParts.map((part, index) => {
                if (part.type === 'text') {
                    return <Text size='body7' key={index}>{part.content}</Text>;
                }

                const value = keyMap[part?.id || ''];

                if (value) {
                    return (
                        <Text key={index} size="body10" variant="primary">
                            {value[locale]}
                        </Text>
                    );
                } else {
                    return <Text key={index}></Text>;
                }
            })}
        </Text>
    );
};