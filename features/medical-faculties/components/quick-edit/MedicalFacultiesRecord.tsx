import { Text } from "@/components/ui/Text/Text"
import { useTranslation } from "react-i18next"
import { ScrollView } from "react-native"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { HeaderDetail } from "../detail/HeaderDetail"
import { BoxSentences } from "./BoxSentences"
import { SymptomsInSentences } from "./SymptomsInSentences"

export const MedicalFacultiesRecord = () => {
    const { t } = useTranslation()

    const insets = useSafeAreaInsets()
    return <ScrollView keyboardShouldPersistTaps="handled"
        keyboardDismissMode="on-drag" className="flex-1" showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: insets.bottom, paddingHorizontal: 16 }} >
        <HeaderDetail />

        <Text className="mt-4" size="body9" variant="error" >
            {t('MES-1033')}
        </Text>

        <BoxSentences />

        <SymptomsInSentences />
    </ScrollView>
}
