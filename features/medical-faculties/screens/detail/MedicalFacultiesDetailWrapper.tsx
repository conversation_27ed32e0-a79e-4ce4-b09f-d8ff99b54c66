import { View } from 'react-native'

import { useEffect } from 'react'
import { HeaderBarDetail } from '../../components/common/HeaderBarDetail'
import { MedicalFacultiesDetailContent } from '../../components/detail/MedicalFacultiesDetailContent'
import { MedicalFacultiesRecord } from '../../components/quick-edit/MedicalFacultiesRecord'
import { MedicalFacultiesContent } from '../../layouts/MedicalFacultiesContent'
import { useMedicalFacultiesStore } from '../../stores/MedicalFacultiesStores'

export const MedicalFacultyDetailWrapper = () => {

  const { isLoading, reset, hiddenLoading, setSentence, setKeywords, isFinishInType } = useMedicalFacultiesStore()

  useEffect(() => {
    let timer: number
    if (isLoading) {
      timer = setTimeout(() => {
        hiddenLoading()
        setSentence({
          vi: '<PERSON><PERSON><PERSON> b<PERSON><PERSON> sĩ, tô<PERSON> bị [677d4fb58d7dec6926ec0fc9] [677d4ff08d7dec6926ec1023] từ [45XETNVB9K], tình trạng nghiêm trọng hơn khi [45XETNVB6K]. Đau nhiều ở [45XETNVB5K].',
          ja: 'こんにちは、[677d4fb58d7dec6926ec0fc9]から[677d4ff08d7dec6926ec1023]と[45XETNVB9K]いがあります。[45XETNVB6K]で症状が悪化します。[45XETNVB5K]が特に痛みます。',
        })

        setKeywords({
          '677d4fb58d7dec6926ec0fc9': {
            id: '677d4fb58d7dec6926ec0fc9',
            name: {
              vi: 'đau đầu',
              ja: '頭痛',
            },
            type: 'symptom',
            hiragana: 'はらいたい',
          } as any,
          '677d4ff08d7dec6926ec1023': {
            id: '677d4ff08d7dec6926ec1023',
            name: {
              vi: 'chóng mặt',
              ja: 'めまい',
            },
            type: 'symptom',
            hiragana: 'あたまいたい',
          } as any,
          '45XETNVB9K': {
            id: '45XETNVB9K',
            name: {
              vi: 'hôm qua',
              ja: '昨日',
            },
            type: 'when',
            hiragana: 'はらいたい',
          } as any,
          '45XETNVB6K': {
            id: '45XETNVB6K',
            name: {
              vi: 'đi lên xuống cầu thang',
              ja: '階段を上り下りする',
            },
            type: 'while',
            hiragana: 'はらいたい',
          } as any,
          '45XETNVB5K': {
            id: '45XETNVB5K',
            name: {
              vi: 'thái dương',
              ja: 'こめかみ',
            },
            type: 'position',
            hiragana: 'はらいたい',
          } as any,

          '45XETNVB4K': {
            id: '45XETNVB4K',
            name: {
              vi: 'Chào bác sĩ',
              ja: 'こんにちは',
            },
            type: 'position',
            hiragana: 'はらいたい',
          } as any,
        })
      }, 3000)

    }

    return () => {
      clearTimeout(timer)
      // reset()
    }

  }, [isLoading, hiddenLoading, setSentence, setKeywords])


  useEffect(() => {
    return () => {
      reset()
    }
  }, [])
  return (
    <View className="z-10 bg-white flex-1 ">
      <HeaderBarDetail />

      <MedicalFacultiesContent>
        {
          !isFinishInType() ? <MedicalFacultiesDetailContent /> : <MedicalFacultiesRecord />
        }
      </MedicalFacultiesContent>

    </View>
  )
}
