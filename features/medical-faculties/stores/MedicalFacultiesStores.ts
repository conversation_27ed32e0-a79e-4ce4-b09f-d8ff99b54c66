import { Keyword } from '@/types/keyword.type'
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { ESymptomsType } from '../types'

export type KeywordByType = Keyword & {
  type: ESymptomsType
}

export interface MedicalFacultiesState {
  isLoading: boolean
  type: 'patient' | 'doctor'
  patientSentence: Record<string, string>
  doctorSentence: Record<string, string>
  keywordsPatient: Record<string, Keyword>
  keywordsDoctor: Record<string, Keyword>
  keywordByType: Record<string, string>
}

export interface MedicalFacultiesActions {
  hiddenLoading: () => void
  showLoading: () => void
  setType: (type: 'patient' | 'doctor') => void
  setSentence: (sentence: Record<string, string>) => void
  setKeywords: (keywords: Record<string, Keyword>) => void
  isFinishInType: () => boolean
  reset: () => void
  getSentence: () => Record<string, string>
  getKeywords: () => Record<string, Keyword>
  updateSentence: (keywords: Record<string, Keyword>) => void
}

const initialState: MedicalFacultiesState = {
  isLoading: false,
  type: 'patient',
  patientSentence: {},
  doctorSentence: {},
  keywordsPatient: {},
  keywordsDoctor: {},
  keywordByType: {}
}

export const useMedicalFacultiesStore = create<MedicalFacultiesState & MedicalFacultiesActions>()(
  devtools((set, get) => ({
    ...initialState,
    hiddenLoading: () => {
      set({ isLoading: false })
    },
    showLoading: () => {
      set({ isLoading: true })
    },
    setType: (type: 'patient' | 'doctor') => {
      set({ type })
    },
    setSentence: (sentence: Record<string, string>) => {
      set((state) => ({
        ...state,
        [state.type === 'patient' ? 'patientSentence' : 'doctorSentence']: sentence,
      }))
    },
    setKeywords: (keywords: Record<string, Keyword>) => {
      set((state) => ({
        ...state,
        keywordByType: handleConvertKeywordByType(keywords),
        [state.type === 'patient' ? 'keywordsPatient' : 'keywordsDoctor']: keywords,
      }))
    },
    isFinishInType: () => {
      return get().type === 'doctor' ? Object.keys(get().keywordsDoctor).length > 0 : Object.keys(get().keywordsPatient).length > 0
    },
    reset: () => {
      set(initialState)
    },

    getSentence: () => {
      return get().type === 'patient' ? get().patientSentence : get().doctorSentence
    },
    getKeywords: () => {
      return get().type === 'patient' ? get().keywordsPatient : get().keywordsDoctor
    },
    updateSentence: (keywords: Record<string, Keyword>) => {
      const oldIdToTypeMap = get().keywordByType
      const newIdToTypeMap = handleConvertKeywordByType(keywords)
      const newSentence = updateTemplateKeywords(get().getSentence(), newIdToTypeMap, oldIdToTypeMap)
      set((state) => {
        const isPatient = state.type === 'patient';

        return {
          [isPatient ? 'patientSentence' : 'doctorSentence']: newSentence,
          [isPatient ? 'keywordsPatient' : 'keywordsDoctor']: keywords,
          keywordByType: newIdToTypeMap,
        };
      });

    }
  })),
)


const handleConvertKeywordByType = (keywords: Record<string, Keyword>): Record<string, string> => {
  const keywordByType: Record<string, string> = {}
  Object.values(keywords).forEach((keyword) => {
    const keywordType = keyword as KeywordByType
    if (keywordType.type) {
      keywordByType[keywordType.id] = (keywordType.type)
    }
  })
  return keywordByType

}

const groupIdsByType = (idToTypeMap: Record<string, string>) => {
  return Object.entries(idToTypeMap).reduce<Record<string, string[]>>(
    (acc, [id, type]) => {
      (acc[type] ||= []).push(id);
      return acc;
    },
    {}
  );
};

const updateTemplateKeywords = (
  templates: Record<string, string>,
  newIdToTypeMap: Record<string, string>,
  oldIdToTypeMap: Record<string, string>
) => {
  const newKeywordsByType = groupIdsByType(newIdToTypeMap);
  const updatedTemplates: Record<string, string> = {};

  const extractOldKeywordMatches = (template: string) => {
    const regex = /\[([^\]]+)\]/g;
    const matches: { match: string; id: string; type: string; start: number; end: number }[] = [];

    let m;
    while ((m = regex.exec(template)) !== null) {
      const id = m[1];
      const type = oldIdToTypeMap[id];
      if (!type) continue;

      matches.push({
        match: m[0],
        id,
        type,
        start: m.index,
        end: m.index + m[0].length
      });
    }

    return matches;
  };

  for (const [lang, template] of Object.entries(templates)) {
    const matches = extractOldKeywordMatches(template);
    if (matches.length === 0) {
      updatedTemplates[lang] = template;
      continue;
    }

    let result = template;
    const processedTypes = new Set<string>();

    for (const { type } of matches) {
      if (processedTypes.has(type)) continue;

      const typeMatches = matches.filter(m => m.type === type);
      const first = typeMatches[0];
      const last = typeMatches[typeMatches.length - 1];

      const replaceZone = template.slice(first.start, last.end);
      const newIds = newKeywordsByType[type] ?? [];
      const replacement = newIds.map(id => `[${id}]`).join(' ');

      const escaped = replaceZone.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
      result = result.replace(new RegExp(escaped, 'g'), replacement);

      processedTypes.add(type);
    }

    updatedTemplates[lang] = result;
  }

  return updatedTemplates;
};
