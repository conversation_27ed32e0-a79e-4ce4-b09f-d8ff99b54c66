import { BodyPart } from './body-part.type'
import { ExaminationForm } from './examination.type'
import { Media } from './media.type'
import { Question } from './question.type'
import { Subscription } from './subscription.type'
import { Symptom } from './symptom.type'

export interface Faculty {
  id: string
  name: string
  description?: string | null
  fallbackFaculty?: boolean | null
  hasQuestions?: boolean | null
  symptoms?: (string | Symptom)[] | null
  bodyParts?: (string | BodyPart)[] | null
  subscription?: (string | Subscription)[] | null
  questions?:
    | {
        step: string
        question: string | Question
        id?: string | null
      }[]
    | null
  examinationForm?: (string | null) | ExaminationForm
  icon?: (string | null) | Media
  mergedText?: string | null
  difyDocumentId?: string | null
  updatedAt: string
  createdAt: string
}
